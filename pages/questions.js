import {
    AppBar,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    CardHeader,
    Checkbox,
    Chip,
    IconButton,
    List,
    ListItem,
    ListItemAvatar,
    ListItemButton,
    ListItemText,
    Paper,
    Toolbar,
    Typography,
    useTheme
} from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

// Icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ComputerIcon from '@mui/icons-material/Computer';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonIcon from '@mui/icons-material/Person';
import QuizIcon from '@mui/icons-material/Quiz';
import SendIcon from '@mui/icons-material/Send';

export default function QuestionsPage() {
  const router = useRouter();
  const theme = useTheme();
  const [history, setHistory] = useState([]);
  const [selectedQuestions, setSelectedQuestions] = useState([]);

  // Load history from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('interviewHistory');
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        setHistory(parsed || []);
      } catch (error) {
        console.error('Error loading history:', error);
      }
    }
  }, []);

  const questions = history.filter(item => item.type === 'question');

  const handleQuestionToggle = (questionId) => {
    setSelectedQuestions(prev => 
      prev.includes(questionId) 
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const handleSelectAll = () => {
    if (selectedQuestions.length === questions.length) {
      setSelectedQuestions([]);
    } else {
      setSelectedQuestions(questions.map(q => q.id));
    }
  };

  const handleDeleteSelected = () => {
    if (selectedQuestions.length === 0) return;
    
    const updatedHistory = history.filter(item => 
      item.type !== 'question' || !selectedQuestions.includes(item.id)
    );
    
    setHistory(updatedHistory);
    localStorage.setItem('interviewHistory', JSON.stringify(updatedHistory));
    setSelectedQuestions([]);
  };

  const handleCombineAndSubmit = () => {
    if (selectedQuestions.length === 0) return;
    
    const selectedQuestionTexts = questions
      .filter(q => selectedQuestions.includes(q.id))
      .map(q => q.text)
      .join('\n\n');
    
    // Store combined questions for the interview page
    localStorage.setItem('combinedQuestions', selectedQuestionTexts);
    
    // Navigate back to interview page
    router.push('/interview?combined=true');
  };

  const renderQuestionItem = (item, index) => {
    const Icon = item.source === 'system' ? ComputerIcon : PersonIcon;
    const title = item.source === 'system' ? 'Interviewer' : 'Candidate';
    const avatarBgColor = item.source === 'system' ? theme.palette.info.main : theme.palette.success.main;
    const isSelected = selectedQuestions.includes(item.id);

    return (
      <ListItem key={`question-${item.id}`} disablePadding>
        <Paper 
          elevation={isSelected ? 3 : 1} 
          sx={{ 
            width: '100%', 
            mb: 1,
            border: isSelected ? 2 : 1,
            borderColor: isSelected ? 'primary.main' : 'divider',
            borderRadius: 2
          }}
        >
          <ListItemButton onClick={() => handleQuestionToggle(item.id)}>
            <Checkbox
              checked={isSelected}
              onChange={() => handleQuestionToggle(item.id)}
              sx={{ mr: 1 }}
            />
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: avatarBgColor, color: 'white' }}>
                <Icon />
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <Typography variant="subtitle2" fontWeight="600">
                    {title}
                  </Typography>
                  <Chip 
                    label={new Date(item.timestamp).toLocaleTimeString()} 
                    size="small" 
                    variant="outlined"
                  />
                </Box>
              }
              secondary={
                <Typography variant="body2" color="text.secondary">
                  {item.text}
                </Typography>
              }
            />
          </ListItemButton>
        </Paper>
      </ListItem>
    );
  };

  return (
    <>
      <Head>
        <title>Question History - MyCopilot</title>
      </Head>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh', bgcolor: 'grey.50' }}>
        {/* Header */}
        <AppBar position="static" color="default" elevation={1}>
          <Toolbar>
            <IconButton 
              edge="start" 
              onClick={() => router.push('/interview')}
              sx={{ mr: 2 }}
            >
              <ArrowBackIcon />
            </IconButton>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>
              <Box sx={{ 
                p: 1, 
                borderRadius: 1, 
                bgcolor: 'warning.main',
                color: 'white'
              }}>
                <QuizIcon />
              </Box>
              <Box>
                <Typography variant="h6" fontWeight="600">
                  Question History
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {questions.length} questions captured
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                onClick={handleSelectAll}
                size="small"
              >
                {selectedQuestions.length === questions.length ? 'Deselect All' : 'Select All'}
              </Button>
              
              {selectedQuestions.length > 0 && (
                <>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={handleDeleteSelected}
                    startIcon={<DeleteIcon />}
                    size="small"
                  >
                    Delete ({selectedQuestions.length})
                  </Button>
                  
                  <Button
                    variant="contained"
                    onClick={handleCombineAndSubmit}
                    startIcon={<SendIcon />}
                    size="small"
                  >
                    Combine & Submit ({selectedQuestions.length})
                  </Button>
                </>
              )}
            </Box>
          </Toolbar>
        </AppBar>

        {/* Content */}
        <Box sx={{ flexGrow: 1, p: 3, overflow: 'hidden' }}>
          <Card sx={{ 
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid',
            borderColor: 'divider',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            borderRadius: 2
          }}>
            <CardHeader
              title="All Questions"
              subheader={`Select questions to combine and submit to AI`}
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            />
            <CardContent sx={{ 
              flexGrow: 1, 
              overflow: 'auto', 
              p: 2,
              minHeight: 0
            }}>
              {questions.length === 0 ? (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  height: '100%',
                  color: 'text.secondary'
                }}>
                  <QuizIcon sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
                  <Typography variant="h6" gutterBottom>
                    No Questions Yet
                  </Typography>
                  <Typography variant="body2" textAlign="center">
                    Start capturing questions in the interview session
                  </Typography>
                  <Button 
                    variant="contained" 
                    onClick={() => router.push('/interview')}
                    sx={{ mt: 2 }}
                  >
                    Go to Interview
                  </Button>
                </Box>
              ) : (
                <List sx={{ p: 0 }}>
                  {questions.map(renderQuestionItem)}
                </List>
              )}
            </CardContent>
          </Card>
        </Box>
      </Box>
    </>
  );
}
