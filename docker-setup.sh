#!/bin/bash

# MyCopilot Docker Setup Script
# This script helps you build and run MyCopilot using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if environment files exist and have API keys
check_env_file() {
    # Check for .env.local first (for local development)
    if [ -f ".env.local" ]; then
        ENV_FILE=".env.local"
    elif [ -f ".env" ]; then
        ENV_FILE=".env"
    else
        print_warning "No environment file found!"
        print_status "Creating .env.local from .env.example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env.local
            print_warning "Please edit .env.local and add your API keys before running the application."
            return 1
        else
            print_error ".env.example file not found. Please create .env.local manually."
            return 1
        fi
    fi

    # Ensure .env exists for Docker Compose (copy from .env.local if needed)
    if [ ! -f ".env" ] && [ -f ".env.local" ]; then
        print_status "Creating .env from .env.local for Docker Compose..."
        cp .env.local .env
    fi

    # Check if at least one API key is configured
    if ! grep -q "NEXT_PUBLIC_.*_API_KEY=.*[^[:space:]]" "$ENV_FILE"; then
        print_warning "No API keys found in $ENV_FILE!"
        print_status "Please add at least one API key (OpenAI, Gemini, Deepgram, or Azure) to $ENV_FILE"
        return 1
    fi

    return 0
}

# Function to build and run production
run_production() {
    print_status "Building and running MyCopilot in production mode..."

    if ! check_env_file; then
        print_error "Please configure your API keys in .env.local first."
        exit 1
    fi

    print_status "Loading environment variables from .env.local..."

    # Build and run with docker-compose
    docker-compose down --remove-orphans
    docker-compose build --no-cache
    docker-compose up -d

    print_success "MyCopilot is running in production mode!"
    print_status "Access the application at: http://localhost:3939"
    print_status "To view logs: docker-compose logs -f"
    print_status "To stop: docker-compose down"
}

# Function to stop all containers
stop_all() {
    print_status "Stopping MyCopilot container..."
    docker-compose down --remove-orphans 2>/dev/null || true
    print_success "Container stopped."
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    stop_all

    # Remove images
    docker rmi mycopilot-mycopilot 2>/dev/null || true

    # Clean up unused resources
    docker system prune -f

    print_success "Cleanup completed."
}

# Function to show logs
show_logs() {
    docker-compose logs -f
}

# Function to show help
show_help() {
    echo "MyCopilot Docker Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start, run, prod    Build and run in production mode (port 3939)"
    echo "  stop               Stop the running container"
    echo "  logs               Show container logs"
    echo "  cleanup            Stop container and clean up Docker resources"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start           # Build and run the application"
    echo "  $0 logs            # Show application logs"
    echo "  $0 stop            # Stop the container"
    echo "  $0 cleanup         # Clean up everything"
    echo ""
    echo "For development, use: npm run dev"
}

# Main script logic
case "${1:-help}" in
    "start"|"run"|"prod"|"production")
        run_production
        ;;
    "stop")
        stop_all
        ;;
    "logs")
        show_logs
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|*)
        show_help
        ;;
esac
