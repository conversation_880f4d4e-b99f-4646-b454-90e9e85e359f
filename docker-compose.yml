services:
  mycopilot:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_DEEPGRAM_API_KEY: ${NEXT_PUBLIC_DEEPGRAM_API_KEY}
        NEXT_PUBLIC_AZURE_SPEECH_KEY: ${NEXT_PUBLIC_AZURE_SPEECH_KEY}
        NEXT_PUBLIC_AZURE_SPEECH_REGION: ${NEXT_PUBLIC_AZURE_SPEECH_REGION}
        NEXT_PUBLIC_OPENAI_API_KEY: ${NEXT_PUBLIC_OPENAI_API_KEY}
        NEXT_PUBLIC_GEMINI_API_KEY: ${NEXT_PUBLIC_GEMINI_API_KEY}
    container_name: mycopilot-app
    ports:
      - "3939:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_DEEPGRAM_API_KEY=${NEXT_PUBLIC_DEEPGRAM_API_KEY}
      - NEXT_PUBLIC_AZURE_SPEECH_KEY=${NEXT_PUBLIC_AZURE_SPEECH_KEY}
      - NEXT_PUBLIC_AZURE_SPEECH_REGION=${NEXT_PUBLIC_AZURE_SPEECH_REGION}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_GEMINI_API_KEY=${NEXT_PUBLIC_GEMINI_API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mycopilot-network

networks:
  mycopilot-network:
    driver: bridge
