/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Ensure environment variables are available
  env: {
    NEXT_PUBLIC_DEEPGRAM_API_KEY: process.env.NEXT_PUBLIC_DEEPGRAM_API_KEY,
    NEXT_PUBLIC_AZURE_SPEECH_KEY: process.env.NEXT_PUBLIC_AZURE_SPEECH_KEY,
    NEXT_PUBLIC_AZURE_SPEECH_REGION: process.env.NEXT_PUBLIC_AZURE_SPEECH_REGION,
    NEXT_PUBLIC_OPENAI_API_KEY: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
    NEXT_PUBLIC_GEMINI_API_KEY: process.env.NEXT_PUBLIC_GEMINI_API_KEY,
  },

  // Enhanced webpack configuration for better WebSocket and audio support
  webpack: (config, { isServer }) => {
    // Don't bundle WebSocket on the server side
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push('ws');
    }

    // Ensure proper handling of audio/media files
    config.module.rules.push({
      test: /\.(mp3|wav|ogg|m4a)$/,
      type: 'asset/resource',
    });

    return config;
  },

  // Headers for better CORS and WebSocket support
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'credentialless',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
