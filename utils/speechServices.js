// Dynamic imports for client-side only
let createClient = null;
let SpeechSDK = null;

// Initialize client-side dependencies
if (typeof window !== 'undefined') {
  import('@deepgram/sdk').then(module => {
    createClient = module.createClient;
  });
  import('microsoft-cognitiveservices-speech-sdk').then(module => {
    SpeechSDK = module;
  });
}



/**
 * Abstract base class for speech services
 */
class SpeechService {
  constructor(config, callbacks) {
    this.config = config;
    this.callbacks = callbacks;
    this.isActive = false;
    this.recognizer = null;
  }

  async start(mediaStream, source) {
    throw new Error('start method must be implemented');
  }

  async stop() {
    throw new Error('stop method must be implemented');
  }

  cleanup() {
    // Default cleanup implementation
    if (this.recognizer) {
      this.recognizer = null;
    }
    this.isActive = false;
  }
}

/**
 * Deepgram Speech Service Implementation (Direct Voice Pattern)
 */
export class DeepgramSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.deepgram = null;
    this.connection = null;
    this.mediaRecorder = null;
    this.connectionTimeout = null;
    this.transcriptionTimeout = null;
    this.hasReceivedResults = false;
    this.keepAliveInterval = null;
  }



  async start(mediaStream, source) {
    try {
      if (!this.config.deepgramKey) {
        throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');
      }

      // Enhanced validation of API key format
      if (typeof this.config.deepgramKey !== 'string' || this.config.deepgramKey.length < 10) {
        throw new Error('Deepgram API key appears to be invalid. Please check your API key in Settings.');
      }

      // Validate API key format (should start with specific pattern)
      if (!this.config.deepgramKey.match(/^[a-f0-9]{40}$/i) && !this.config.deepgramKey.includes('_')) {
        console.warn('Deepgram API key format may be incorrect. Expected format: 40-character hex string or token with underscores.');
      }

      if (!createClient) {
        const { createClient: dgCreateClient } = await import('@deepgram/sdk');
        createClient = dgCreateClient;
      }

      console.log(`Starting Deepgram Speech Service for ${source}...`);

      // Create Deepgram client with proper configuration
      try {
        this.deepgram = createClient(this.config.deepgramKey);
        console.log(`✅ Deepgram client created successfully for ${source}`);
      } catch (clientError) {
        console.error(`Failed to create Deepgram client for ${source}:`, clientError);
        throw new Error(`Failed to create Deepgram client: ${clientError.message}`);
      }

      // Create MediaRecorder for direct audio streaming (similar to Azure Speech pattern)
      try {
        // Use webm format for better browser compatibility
        const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus')
          ? 'audio/webm;codecs=opus'
          : 'audio/webm';

        this.mediaRecorder = new MediaRecorder(mediaStream, {
          mimeType: mimeType,
          audioBitsPerSecond: 16000
        });

        console.log(`✅ MediaRecorder created for ${source} with mimeType: ${mimeType}`);
      } catch (recorderError) {
        console.error(`Failed to create MediaRecorder for ${source}:`, recorderError);
        throw new Error(`Failed to create MediaRecorder: ${recorderError.message}`);
      }

      // Create WebSocket connection for real-time transcription
      try {
        const connectionOptions = {
          model: 'nova-2',
          language: 'en-US',
          interim_results: true,
          smart_format: true,
          punctuate: true,
          encoding: 'webm',
          sample_rate: 16000,
          channels: 1
        };

        console.log(`Creating Deepgram connection for ${source} with options:`, connectionOptions);
        this.connection = this.deepgram.listen.live(connectionOptions);
        console.log(`✅ Deepgram connection created for ${source}`);
      } catch (connectionError) {
        console.error(`Failed to create Deepgram connection for ${source}:`, connectionError);
        throw new Error(`Failed to create Deepgram connection: ${connectionError.message}`);
      }

      // Set up Deepgram connection event handlers (similar to Azure Speech pattern)
      this.connection.on('open', () => {
        console.log(`✅ Deepgram connection opened for ${source}`);
        this.isActive = true;

        // Clear any connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        // Clear transcription timeout since connection is working
        if (this.transcriptionTimeout) {
          clearTimeout(this.transcriptionTimeout);
          this.transcriptionTimeout = null;
        }

        // Send a keep-alive message to ensure connection stays active
        this.keepAliveInterval = setInterval(() => {
          if (this.connection && this.isActive) {
            try {
              this.connection.keepAlive();
            } catch (e) {
              // Ignore keep-alive errors
            }
          }
        }, 30000); // Every 30 seconds

        // Start MediaRecorder once connection is ready
        this.startMediaRecorder();

        this.callbacks.onStart?.(source);
        console.log(`🚀 Deepgram ready, MediaRecorder started for ${source}`);
      });

      this.connection.on('Results', (data) => {
        console.log(`🎯 Deepgram Results received for ${source}:`, data);
        this.hasReceivedResults = true; // Mark that we've received results

        const transcript = data.channel?.alternatives?.[0]?.transcript;
        console.log(`📝 Extracted transcript: "${transcript}"`);

        if (transcript && transcript.trim()) {
          if (data.is_final) {
            console.log(`✅ Final transcript for ${source}: "${transcript}"`);
            this.callbacks.onFinalResult?.(transcript, source);
          } else {
            console.log(`⏳ Interim transcript for ${source}: "${transcript}"`);
            this.callbacks.onInterimResult?.(transcript, source);
          }
        } else {
          // Log when we get results but no transcript
          console.log(`⚠️ Deepgram results received but no transcript for ${source}:`, {
            hasChannel: !!data.channel,
            hasAlternatives: !!data.channel?.alternatives,
            alternativesLength: data.channel?.alternatives?.length,
            firstAlternative: data.channel?.alternatives?.[0],
            transcript: transcript,
            rawData: data
          });
        }
      });

      this.connection.on('Metadata', (data) => {
        console.log(`📊 Deepgram metadata for ${source}:`, data);
      });

      // Listen for any other events that might give us clues
      this.connection.on('UtteranceEnd', (data) => {
        console.log(`🔚 Deepgram UtteranceEnd for ${source}:`, data);
      });

      this.connection.on('SpeechStarted', (data) => {
        console.log(`🎤 Deepgram SpeechStarted for ${source}:`, data);
      });

      this.connection.on('error', (error) => {
        console.error(`Deepgram error for ${source}:`, error);
        console.error(`Error details:`, {
          message: error.message,
          type: error.type,
          code: error.code,
          stack: error.stack
        });

        // Provide more specific error messages based on error type
        let errorMessage = 'Deepgram WebSocket connection error.';

        if (error.message) {
          if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            errorMessage = 'Deepgram API key is invalid or expired. Please check your API key in Settings.';
          } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
            errorMessage = 'Deepgram API key does not have permission for real-time transcription. Please check your plan.';
          } else if (error.message.includes('429') || error.message.includes('rate limit')) {
            errorMessage = 'Deepgram rate limit exceeded. Please wait a moment and try again.';
          } else if (error.message.includes('network') || error.message.includes('ENOTFOUND')) {
            errorMessage = 'Network connection error. Please check your internet connection.';
          } else if (error.message.includes('WebSocket')) {
            errorMessage = 'WebSocket connection failed. This may be due to browser compatibility or network restrictions.';
          } else {
            errorMessage = `Deepgram connection error: ${error.message}`;
          }
        }

        this.callbacks.onError?.(new Error(errorMessage), source);
        this.stop();
      });

      this.connection.on('close', (closeEvent) => {
        console.log(`Deepgram connection closed for ${source}:`, closeEvent);
        console.log(`Close event details:`, {
          code: closeEvent?.code,
          reason: closeEvent?.reason,
          wasClean: closeEvent?.wasClean,
          hasReceivedResults: this.hasReceivedResults
        });
        this.isActive = false;

        // Enhanced close event handling with specific error codes
        if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {
          console.error(`Deepgram connection closed with error code ${closeEvent.code}: ${closeEvent.reason}`);

          let errorMessage = `Deepgram connection closed unexpectedly (code: ${closeEvent.code}).`;

          // Provide specific messages for common close codes
          switch (closeEvent.code) {
            case 1002:
              errorMessage = 'Deepgram connection closed due to protocol error. This may be a browser compatibility issue.';
              break;
            case 1006:
              errorMessage = 'Deepgram connection closed abnormally. This may be due to network issues or firewall restrictions.';
              break;
            case 1011:
              errorMessage = 'Deepgram server error. Please try again in a moment.';
              break;
            case 1012:
              errorMessage = 'Deepgram service is restarting. Please try again in a moment.';
              break;
            case 4001:
              errorMessage = 'Deepgram authentication failed. Please check your API key.';
              break;
            case 4002:
              errorMessage = 'Deepgram authorization failed. Your API key may not have the required permissions.';
              break;
            default:
              if (closeEvent.reason) {
                errorMessage += ` Reason: ${closeEvent.reason}`;
              }
          }

          this.callbacks.onError?.(new Error(errorMessage), source);
        } else {
          // Normal close - just stop
          this.callbacks.onStop?.(source);
        }
      });

      this.connection.on('warning', (warning) => {
        console.warn(`Deepgram warning for ${source}:`, warning);
      });

      // Set up MediaRecorder event handlers (direct approach like Azure Speech)
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && this.connection && this.isActive) {
          console.log(`📤 Sending ${event.data.size} bytes of audio data to Deepgram for ${source}`);
          try {
            this.connection.send(event.data);
          } catch (sendError) {
            console.error(`Failed to send audio data to Deepgram for ${source}:`, sendError);
          }
        }
      };

      this.mediaRecorder.onstart = () => {
        console.log(`🎤 MediaRecorder started for ${source}`);
      };

      this.mediaRecorder.onstop = () => {
        console.log(`⏹️ MediaRecorder stopped for ${source}`);
      };

      this.mediaRecorder.onerror = (error) => {
        console.error(`MediaRecorder error for ${source}:`, error);
        this.callbacks.onError?.(new Error(`MediaRecorder error: ${error.error}`), source);
      };





      // Set connection timeout
      this.connectionTimeout = setTimeout(() => {
        if (!this.isActive) {
          console.error(`Deepgram connection timeout for ${source} after 10 seconds`);
          const timeoutError = new Error('Deepgram connection timeout. Please check your network connection and API key.');
          this.callbacks.onError?.(timeoutError, source);
          this.stop();
        }
      }, 10000);

      // Set transcription timeout
      this.transcriptionTimeout = setTimeout(() => {
        if (this.isActive && !this.hasReceivedResults) {
          console.warn(`⚠️ No transcription results received from Deepgram for ${source} after 15 seconds.`);
          const noResultsError = new Error('No speech detected yet. Please check your microphone and try speaking clearly.');
          this.callbacks.onError?.(noResultsError, source);
        }
      }, 15000);

      return this;
    } catch (error) {
      console.error(`Failed to start Deepgram for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  // Start MediaRecorder with regular intervals (similar to Azure Speech continuous recognition)
  startMediaRecorder() {
    try {
      // Start recording with 250ms intervals for real-time streaming
      this.mediaRecorder.start(250);
      console.log(`✅ MediaRecorder started with 250ms intervals`);
    } catch (error) {
      console.error('Failed to start MediaRecorder:', error);
      this.callbacks.onError?.(new Error(`Failed to start audio recording: ${error.message}`));
    }
  }

  async stop() {
    try {
      this.isActive = false;

      // Stop MediaRecorder (similar to Azure Speech stopContinuousRecognitionAsync)
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
        this.mediaRecorder = null;
      }

      // Close Deepgram connection
      if (this.connection) {
        this.connection.finish();
        this.connection = null;
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Deepgram:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
    if (this.transcriptionTimeout) {
      clearTimeout(this.transcriptionTimeout);
      this.transcriptionTimeout = null;
    }
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
      this.keepAliveInterval = null;
    }
    this.mediaRecorder = null;
    this.connection = null;
    this.deepgram = null;
    this.hasReceivedResults = false;
  }
}

/**
 * Azure Speech Service Implementation (Legacy)
 */
export class AzureSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.audioConfig = null;
    this.speechConfig = null;
  }

  async start(mediaStream, source) {
    try {
      if (!this.config.azureToken || !this.config.azureRegion) {
        throw new Error('Azure Speech credentials are required');
      }

      if (!SpeechSDK) {
        const module = await import('microsoft-cognitiveservices-speech-sdk');
        SpeechSDK = module.default || module;
      }

      console.log(`Starting Azure Speech Service for ${source}...`);

      // Create audio config from media stream
      try {
        this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);
        console.log(`✅ Azure AudioConfig created for ${source}`);
      } catch (audioError) {
        console.error(`Failed to create Azure AudioConfig for ${source}:`, audioError);
        throw new Error(`Failed to setup audio input: ${audioError.message}`);
      }

      // Create speech config
      try {
        this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
          this.config.azureToken,
          this.config.azureRegion
        );
        this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';
        console.log(`✅ Azure SpeechConfig created for ${source} (language: ${this.speechConfig.speechRecognitionLanguage})`);
      } catch (configError) {
        console.error(`Failed to create Azure SpeechConfig for ${source}:`, configError);
        throw new Error(`Failed to setup speech configuration: ${configError.message}`);
      }

      // Create recognizer
      try {
        this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);
        console.log(`✅ Azure SpeechRecognizer created for ${source}`);
      } catch (recognizerError) {
        console.error(`Failed to create Azure SpeechRecognizer for ${source}:`, recognizerError);
        throw new Error(`Failed to create speech recognizer: ${recognizerError.message}`);
      }

      // Set up event handlers
      this.recognizer.recognizing = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {
          console.log(`⏳ Azure interim result for ${source}: "${e.result.text}"`);
          this.callbacks.onInterimResult?.(e.result.text, source);
        }
      };

      this.recognizer.recognized = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {
          console.log(`✅ Azure final result for ${source}: "${e.result.text}"`);
          this.callbacks.onFinalResult?.(e.result.text, source);
        } else if (e.result.reason === SpeechSDK.ResultReason.NoMatch) {
          console.log(`🔇 Azure no match for ${source} (silence or unclear speech)`);
        }
      };

      this.recognizer.canceled = (s, e) => {
        console.log(`Azure recognition canceled for ${source}: ${e.reason}`);
        if (e.reason === SpeechSDK.CancellationReason.Error) {
          console.error(`Azure Speech error for ${source}: ${e.errorDetails}`);
          const error = new Error(`Azure Speech error: ${e.errorDetails}`);
          this.callbacks.onError?.(error, source);
        }
        this.stop();
      };

      this.recognizer.sessionStopped = (s, e) => {
        console.log(`Azure session stopped for ${source}`);
        this.callbacks.onStop?.(source);
        this.stop();
      };

      // Start continuous recognition
      try {
        await this.recognizer.startContinuousRecognitionAsync();
        this.isActive = true;
        console.log(`🚀 Azure Speech recognition started for ${source}`);
        this.callbacks.onStart?.(source);
      } catch (startError) {
        console.error(`Failed to start Azure recognition for ${source}:`, startError);
        throw new Error(`Failed to start speech recognition: ${startError.message}`);
      }

      return this;
    } catch (error) {
      console.error(`Failed to start Azure Speech for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  async stop() {
    try {
      this.isActive = false;

      if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {
        await this.recognizer.stopContinuousRecognitionAsync();
      }

      if (this.audioConfig && typeof this.audioConfig.close === 'function') {
        this.audioConfig.close();
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Azure Speech:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    this.audioConfig = null;
    this.speechConfig = null;
  }
}

/**
 * Factory function to create speech service instances
 */
export function createSpeechService(config, callbacks) {
  const serviceType = config.speechService || 'deepgram';
  
  switch (serviceType) {
    case 'deepgram':
      return new DeepgramSpeechService(config, callbacks);
    case 'azure':
      return new AzureSpeechService(config, callbacks);
    default:
      throw new Error(`Unknown speech service: ${serviceType}`);
  }
}
